# Component Architecture Guide

## 📁 Recommended Folder Structure

```
src/
├── app/                          # Next.js App Router
│   ├── (routes)/                 # Route groups for organization
│   │   ├── artist/
│   │   │   ├── [id]/
│   │   │   │   ├── page.tsx      # Clean, minimal page file
│   │   │   │   └── loading.tsx
│   │   │   └── page.tsx
│   │   ├── playlist/
│   │   └── search/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/                   # Shared components across the app
│   ├── ui/                      # Basic UI building blocks
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.types.ts
│   │   │   └── index.ts
│   │   ├── Card/
│   │   ├── Modal/
│   │   └── index.ts             # Barrel exports
│   ├── layout/                  # Layout-related components
│   │   ├── Header/
│   │   ├── Sidebar/
│   │   ├── Footer/
│   │   └── Navigation/
│   ├── features/                # Feature-specific shared components
│   │   ├── audio/
│   │   │   ├── AudioPlayer/
│   │   │   ├── VolumeControl/
│   │   │   └── PlaylistQueue/
│   │   ├── search/
│   │   │   ├── SearchBar/
│   │   │   └── SearchResults/
│   │   └── user/
│   │       ├── UserProfile/
│   │       └── UserPreferences/
│   ├── common/                  # Utility components
│   │   ├── ClientOnly/
│   │   ├── ErrorBoundary/
│   │   ├── LoadingSpinner/
│   │   └── ThemeToggle/
│   └── providers/               # Context providers
│       ├── ThemeProvider/
│       ├── AudioProvider/
│       └── UserProvider/
├── features/                    # Route-specific feature modules
│   ├── artist/                  # Artist page specific components
│   │   ├── components/          # Components specific to artist pages
│   │   │   ├── ArtistHeader/
│   │   │   │   ├── ArtistHeader.tsx
│   │   │   │   ├── ArtistHeader.types.ts
│   │   │   │   ├── ArtistHeader.module.css (optional)
│   │   │   │   └── index.ts
│   │   │   ├── ArtistBio/
│   │   │   ├── ArtistDiscography/
│   │   │   ├── ArtistStats/
│   │   │   ├── RelatedArtists/
│   │   │   └── TopTracks/
│   │   ├── hooks/               # Artist-specific hooks
│   │   │   ├── useArtistData.ts
│   │   │   ├── useArtistTracks.ts
│   │   │   └── useRelatedArtists.ts
│   │   ├── types/               # Artist-specific types
│   │   │   ├── artist.types.ts
│   │   │   └── index.ts
│   │   ├── utils/               # Artist-specific utilities
│   │   │   ├── artistHelpers.ts
│   │   │   └── formatters.ts
│   │   └── index.ts             # Main export for artist features
│   ├── playlist/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── types/
│   │   └── utils/
│   └── search/
│       ├── components/
│       ├── hooks/
│       ├── types/
│       └── utils/
├── lib/                         # Shared utilities and configurations
│   ├── api/                     # API layer
│   │   ├── client.ts
│   │   ├── endpoints/
│   │   └── types/
│   ├── utils/                   # General utilities
│   │   ├── cn.ts               # Class name utility
│   │   ├── formatters.ts
│   │   └── validators.ts
│   ├── hooks/                   # Global hooks
│   │   ├── useLocalStorage.ts
│   │   ├── useDebounce.ts
│   │   └── useApi.ts
│   └── constants/               # App constants
│       ├── routes.ts
│       ├── api.ts
│       └── theme.ts
├── types/                       # Global TypeScript types
│   ├── api.ts
│   ├── common.ts
│   └── index.ts
└── styles/                      # Global styles and themes
    ├── globals.css
    ├── components.css
    └── themes/
```

## 🏗️ Component Organization Principles

### 1. Route-Specific Components (`features/`)
- Components that are specific to a particular route or feature
- Should not be imported by other routes
- Contains business logic specific to that feature
- Organized by feature domain (artist, playlist, search, etc.)

### 2. Shared Components (`components/`)
- Reusable across multiple routes
- Organized by purpose: ui, layout, features, common
- Should be generic and configurable
- No route-specific business logic

### 3. Clean Page Files
- Minimal composition and layout
- Import from feature modules
- Handle route-level concerns (params, searchParams)
- Delegate complex logic to feature components

## 📝 Naming Conventions

### Component Files
- **PascalCase** for component names: `ArtistHeader.tsx`
- **Folder per component** for complex components
- **Index files** for clean imports
- **Type files** with `.types.ts` suffix

### Hooks
- **camelCase** starting with `use`: `useArtistData.ts`
- **Feature-specific** hooks in feature folders
- **Global hooks** in `lib/hooks/`

### Types
- **PascalCase** for type names: `ArtistData`
- **Feature-specific** types in feature folders
- **Global types** in `types/` folder

### Utilities
- **camelCase** for function names
- **Feature-specific** utils in feature folders
- **Global utils** in `lib/utils/`

## 🎯 Decision Guidelines

### When to Create Route-Specific vs Shared Components

#### Route-Specific Components (`features/`)
Create in `features/` when:
- Component contains business logic specific to a route
- Component uses route-specific data structures
- Component is unlikely to be reused elsewhere
- Component handles route-specific user interactions

**Example**: `ArtistHeader` - Contains artist-specific data, follow/unfollow logic, and social links

#### Shared Components (`components/`)
Create in `components/` when:
- Component is purely presentational
- Component can be reused across multiple routes
- Component provides common UI patterns
- Component has no business logic dependencies

**Example**: `Button` - Generic UI component with variants and sizes

### Component Composition Patterns

#### 1. Container/Presentation Pattern
```typescript
// Container (in features/)
function ArtistPageContainer({ artistId }: { artistId: string }) {
  const { artist, isLoading } = useArtistData(artistId);

  return (
    <ArtistHeader
      artist={artist}
      isLoading={isLoading}
      onFollow={handleFollow}
    />
  );
}

// Presentation (can be shared)
function ArtistHeader({ artist, isLoading, onFollow }: ArtistHeaderProps) {
  // Pure presentation logic
}
```

#### 2. Feature Composition
```typescript
// Clean page file
export default function ArtistPage({ params }: { params: { id: string } }) {
  return (
    <div>
      <ArtistHeader artistId={params.id} />
      <TopTracks artistId={params.id} />
      <ArtistDiscography artistId={params.id} />
    </div>
  );
}
```

## 🔄 Data Flow Best Practices

### 1. Prop Drilling vs Context
- **Props**: For 1-2 levels of component nesting
- **Context**: For deeply nested or widely used state
- **Custom Hooks**: For complex state logic

### 2. Server vs Client Components
- **Server Components**: For static content and initial data
- **Client Components**: For interactive features and real-time updates
- **Hybrid**: Use both strategically for optimal performance

### 3. Error Boundaries
```typescript
// In each feature folder
function ArtistErrorBoundary({ children }: { children: React.ReactNode }) {
  // Handle artist-specific errors
}
```

## 📦 Import/Export Patterns

### Barrel Exports
Use index.ts files for clean imports:
```typescript
// Instead of:
import ArtistHeader from '@/features/artist/components/ArtistHeader/ArtistHeader';
import TopTracks from '@/features/artist/components/TopTracks/TopTracks';

// Use:
import { ArtistHeader, TopTracks } from '@/features/artist';
```

### Absolute Imports
Configure path mapping in tsconfig.json:
```json
{
  "paths": {
    "@/*": ["./src/*"],
    "@/components/*": ["./src/components/*"],
    "@/features/*": ["./src/features/*"],
    "@/lib/*": ["./src/lib/*"]
  }
}
```

## 🧪 Testing Strategy

### Component Testing Structure
```
src/features/artist/
├── components/
│   ├── ArtistHeader/
│   │   ├── ArtistHeader.tsx
│   │   ├── ArtistHeader.test.tsx
│   │   └── ArtistHeader.stories.tsx (Storybook)
```

### Test Categories
1. **Unit Tests**: Individual component behavior
2. **Integration Tests**: Component interactions
3. **E2E Tests**: Full user workflows

## 🚀 Scalability Considerations

### Code Splitting
- Route-level splitting (automatic with Next.js)
- Feature-level splitting with dynamic imports
- Component-level splitting for heavy components

### Performance Optimization
- Lazy loading for non-critical components
- Memoization for expensive calculations
- Virtual scrolling for large lists

### Team Collaboration
- Clear ownership boundaries (features vs shared)
- Consistent naming conventions
- Documented component APIs
- Shared design system components

## 📋 Implementation Checklist

### For New Features
- [ ] Create feature folder structure
- [ ] Define TypeScript types
- [ ] Implement custom hooks for data fetching
- [ ] Create route-specific components
- [ ] Add error boundaries
- [ ] Write tests
- [ ] Update barrel exports

### For Shared Components
- [ ] Design generic, configurable API
- [ ] Add comprehensive prop types
- [ ] Include loading and error states
- [ ] Write Storybook stories
- [ ] Add accessibility features
- [ ] Document usage examples

## 🔧 Development Workflow

### 1. Start with Types
Define your data structures and component props first

### 2. Build from the Bottom Up
Start with shared UI components, then feature-specific components

### 3. Keep Pages Minimal
Pages should primarily handle routing concerns and compose features

### 4. Iterate and Refactor
Move components between shared/feature folders as patterns emerge

This architecture provides a solid foundation for scaling your Next.js application while maintaining clean, maintainable code that multiple developers can work on effectively.
