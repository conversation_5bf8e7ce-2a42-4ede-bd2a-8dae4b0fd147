'use client';

import { useTheme } from '@/contexts/ThemeContext';

export default function ThemeDemo() {
  const { theme } = useTheme();

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-card-foreground mb-4">
          Theme Demo
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Current theme:</span>
            <span className="text-sm font-medium text-foreground capitalize">
              {theme}
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="bg-primary text-primary-foreground px-3 py-2 rounded text-sm">
              Primary Button
            </div>
            <div className="bg-secondary text-secondary-foreground px-3 py-2 rounded text-sm">
              Secondary Button
            </div>
            <div className="bg-muted text-muted-foreground px-3 py-2 rounded text-sm">
              Muted Background
            </div>
            <div className="bg-accent text-accent-foreground px-3 py-2 rounded text-sm">
              Accent Background
            </div>
          </div>
          
          <div className="border border-border rounded p-3">
            <p className="text-sm text-foreground">
              This is a card with border styling that adapts to the current theme.
            </p>
          </div>
          
          <div className="bg-destructive text-destructive-foreground px-3 py-2 rounded text-sm">
            Destructive/Error State
          </div>
        </div>
      </div>
    </div>
  );
}
