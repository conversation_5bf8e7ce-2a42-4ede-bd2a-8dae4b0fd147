'use client';

import { useState } from 'react';
import { TopTracksProps } from '../../types';

export default function TopTracks({
  tracks,
  onTrackPlay,
  currentPlayingId,
  isLoading = false,
}: TopTracksProps) {
  const [hoveredTrack, setHoveredTrack] = useState<string | null>(null);

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatPlayCount = (count: number): string => {
    if (count >= 1000000000) {
      return `${(count / 1000000000).toFixed(1)}B`;
    }
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-foreground">Popular Tracks</h2>
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center gap-4 p-3 rounded-lg">
              <div className="w-12 h-12 bg-muted animate-pulse rounded" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted animate-pulse rounded w-3/4" />
                <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
              </div>
              <div className="h-4 bg-muted animate-pulse rounded w-16" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!tracks || tracks.length === 0) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-foreground">Popular Tracks</h2>
        <div className="text-center py-8 text-muted-foreground">
          No tracks available
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold text-foreground">Popular Tracks</h2>
      
      <div className="space-y-1">
        {tracks.map((track, index) => {
          const isPlaying = currentPlayingId === track.id;
          const isHovered = hoveredTrack === track.id;
          
          return (
            <div
              key={track.id}
              className={`flex items-center gap-4 p-3 rounded-lg transition-colors cursor-pointer group ${
                isHovered ? 'bg-accent' : 'hover:bg-accent/50'
              }`}
              onMouseEnter={() => setHoveredTrack(track.id)}
              onMouseLeave={() => setHoveredTrack(null)}
              onClick={() => onTrackPlay?.(track)}
            >
              {/* Track Number / Play Button */}
              <div className="w-6 flex items-center justify-center">
                {isPlaying ? (
                  <div className="w-4 h-4 flex items-center justify-center">
                    <div className="flex gap-0.5">
                      <div className="w-0.5 h-3 bg-primary animate-pulse" />
                      <div className="w-0.5 h-3 bg-primary animate-pulse delay-75" />
                      <div className="w-0.5 h-3 bg-primary animate-pulse delay-150" />
                    </div>
                  </div>
                ) : isHovered ? (
                  <svg
                    className="w-4 h-4 text-foreground"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <span className="text-sm text-muted-foreground font-medium">
                    {index + 1}
                  </span>
                )}
              </div>

              {/* Album Cover */}
              {track.albumCover ? (
                <img
                  src={track.albumCover}
                  alt={`${track.albumName} cover`}
                  className="w-12 h-12 rounded object-cover"
                />
              ) : (
                <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-muted-foreground"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 3a1 1 0 00-1.447-.894L8.763 6H5a3 3 0 000 6h.28l1.771 5.316A1 1 0 008 18h1a1 1 0 001-1v-4.382l6.553 3.276A1 1 0 0018 15V3z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h3
                    className={`font-medium truncate ${
                      isPlaying ? 'text-primary' : 'text-foreground'
                    }`}
                  >
                    {track.title}
                  </h3>
                  {track.explicit && (
                    <span className="px-1 py-0.5 bg-muted text-muted-foreground text-xs rounded">
                      E
                    </span>
                  )}
                </div>
                {track.albumName && (
                  <p className="text-sm text-muted-foreground truncate">
                    {track.albumName}
                  </p>
                )}
              </div>

              {/* Play Count */}
              <div className="hidden sm:block text-sm text-muted-foreground">
                {formatPlayCount(track.playCount)}
              </div>

              {/* Duration */}
              <div className="text-sm text-muted-foreground font-mono">
                {formatDuration(track.duration)}
              </div>

              {/* More Options */}
              <button
                className="opacity-0 group-hover:opacity-100 p-1 hover:bg-muted rounded transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle more options
                }}
              >
                <svg
                  className="w-4 h-4 text-muted-foreground"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                </svg>
              </button>
            </div>
          );
        })}
      </div>

      {tracks.length >= 5 && (
        <button className="text-sm text-muted-foreground hover:text-foreground transition-colors">
          Show all tracks
        </button>
      )}
    </div>
  );
}
