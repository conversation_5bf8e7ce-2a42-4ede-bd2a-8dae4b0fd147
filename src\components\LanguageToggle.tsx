'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Languages } from 'lucide-react';

export default function LanguageToggle() {
  const { language, setLanguage } = useLanguage();

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' }
  ] as const;

  const currentIndex = languages.findIndex(lang => lang.code === language);
  const nextLanguage = languages[(currentIndex + 1) % languages.length];

  const handleToggle = () => {
    setLanguage(nextLanguage.code);
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleToggle}
      className="gap-2"
    >
      <Languages className="h-4 w-4" />
      {languages.find(lang => lang.code === language)?.name}
    </Button>
  );
}
