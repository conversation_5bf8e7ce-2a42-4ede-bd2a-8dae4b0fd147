// Artist-specific TypeScript types

export interface Artist {
  id: string;
  name: string;
  bio?: string;
  imageUrl?: string;
  genres: string[];
  followers: number;
  verified: boolean;
  monthlyListeners: number;
  socialLinks?: {
    spotify?: string;
    instagram?: string;
    twitter?: string;
    website?: string;
  };
}

export interface Track {
  id: string;
  title: string;
  duration: number; // in seconds
  playCount: number;
  albumId?: string;
  albumName?: string;
  albumCover?: string;
  releaseDate: string;
  explicit: boolean;
  previewUrl?: string;
}

export interface Album {
  id: string;
  title: string;
  releaseDate: string;
  coverUrl?: string;
  trackCount: number;
  type: 'album' | 'single' | 'ep';
  totalDuration: number;
}

export interface ArtistStats {
  totalTracks: number;
  totalAlbums: number;
  totalPlaytime: number;
  topGenres: string[];
  careerStartYear: number;
}

export interface RelatedArtist {
  id: string;
  name: string;
  imageUrl?: string;
  followers: number;
  similarity: number; // 0-1 score
}

export interface ArtistPageData {
  artist: Artist;
  topTracks: Track[];
  albums: Album[];
  relatedArtists: RelatedArtist[];
  stats: ArtistStats;
}

// API Response types
export interface ArtistApiResponse {
  data: Artist;
  success: boolean;
  message?: string;
}

export interface ArtistTracksApiResponse {
  data: Track[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
  success: boolean;
}

// Component Props types
export interface ArtistHeaderProps {
  artist: Artist;
  isFollowing?: boolean;
  onFollow?: () => void;
  onUnfollow?: () => void;
  isLoading?: boolean;
}

export interface ArtistBioProps {
  bio: string;
  maxLength?: number;
  showReadMore?: boolean;
}

export interface TopTracksProps {
  tracks: Track[];
  onTrackPlay?: (track: Track) => void;
  currentPlayingId?: string;
  isLoading?: boolean;
}

export interface ArtistDiscographyProps {
  albums: Album[];
  onAlbumClick?: (album: Album) => void;
  viewMode?: 'grid' | 'list';
  sortBy?: 'release_date' | 'name' | 'popularity';
}

export interface RelatedArtistsProps {
  artists: RelatedArtist[];
  onArtistClick?: (artist: RelatedArtist) => void;
  maxItems?: number;
}

export interface ArtistStatsProps {
  stats: ArtistStats;
  showDetailed?: boolean;
}
