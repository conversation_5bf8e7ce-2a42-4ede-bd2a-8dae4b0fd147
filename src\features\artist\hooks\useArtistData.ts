'use client';

import { useState, useEffect } from 'react';
import { Artist, ArtistApiResponse } from '../types';

interface UseArtistDataReturn {
  artist: Artist | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useArtistData(artistId: string): UseArtistDataReturn {
  const [artist, setArtist] = useState<Artist | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchArtistData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // TODO: Replace with actual API call
      const response = await fetch(`/api/artists/${artistId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch artist: ${response.statusText}`);
      }
      
      const data: ArtistApiResponse = await response.json();
      
      if (!data.success) {
        throw new Error(data.message || 'Failed to fetch artist data');
      }
      
      setArtist(data.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setArtist(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (artistId) {
      fetchArtistData();
    }
  }, [artistId]);

  const refetch = () => {
    fetchArtistData();
  };

  return {
    artist,
    isLoading,
    error,
    refetch,
  };
}

// Mock data for development
export function useMockArtistData(artistId: string): UseArtistDataReturn {
  const [artist, setArtist] = useState<Artist | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      const mockArtist: Artist = {
        id: artistId,
        name: 'The Weeknd',
        bio: 'Abel Makkonen Tesfaye, known professionally as The Weeknd, is a Canadian singer, songwriter, and record producer. He is noted for his unconventional music production, artistic reinvention, and his signature use of the falsetto register.',
        imageUrl: 'https://example.com/weeknd.jpg',
        genres: ['R&B', 'Pop', 'Alternative R&B'],
        followers: 45000000,
        verified: true,
        monthlyListeners: 85000000,
        socialLinks: {
          spotify: 'https://open.spotify.com/artist/1Xyo4u8uXC1ZmMpatF05PJ',
          instagram: 'https://instagram.com/theweeknd',
          twitter: 'https://twitter.com/theweeknd',
        },
      };
      
      setArtist(mockArtist);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [artistId]);

  const refetch = () => {
    setIsLoading(true);
    // Re-trigger the effect
    setArtist(null);
  };

  return {
    artist,
    isLoading,
    error,
    refetch,
  };
}
