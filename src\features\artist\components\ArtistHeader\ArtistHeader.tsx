'use client';

import { useState } from 'react';
import Image from 'next/image';
import { ArtistHeaderProps } from '../../types';

export default function ArtistHeader({
  artist,
  isFollowing = false,
  onFollow,
  onUnfollow,
  isLoading = false,
}: ArtistHeaderProps) {
  const [imageError, setImageError] = useState(false);

  const handleFollowClick = () => {
    if (isFollowing) {
      onUnfollow?.();
    } else {
      onFollow?.();
    }
  };

  const formatFollowers = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const formatMonthlyListeners = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (isLoading) {
    return (
      <div className="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-b from-primary/10 to-background">
        <div className="w-48 h-48 bg-muted animate-pulse rounded-lg" />
        <div className="flex-1 space-y-4">
          <div className="h-8 bg-muted animate-pulse rounded w-3/4" />
          <div className="h-4 bg-muted animate-pulse rounded w-1/2" />
          <div className="h-4 bg-muted animate-pulse rounded w-1/3" />
          <div className="h-10 bg-muted animate-pulse rounded w-32" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row gap-6 p-6 bg-gradient-to-b from-primary/10 to-background">
      {/* Artist Image */}
      <div className="relative w-48 h-48 mx-auto md:mx-0 flex-shrink-0">
        {artist.imageUrl && !imageError ? (
          <Image
            src={artist.imageUrl}
            alt={`${artist.name} profile picture`}
            fill
            className="object-cover rounded-lg shadow-lg"
            onError={() => setImageError(true)}
            priority
          />
        ) : (
          <div className="w-full h-full bg-muted rounded-lg flex items-center justify-center">
            <div className="text-muted-foreground text-4xl font-bold">
              {artist.name.charAt(0).toUpperCase()}
            </div>
          </div>
        )}
        {artist.verified && (
          <div className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full p-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        )}
      </div>

      {/* Artist Info */}
      <div className="flex-1 space-y-4 text-center md:text-left">
        <div>
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-2">
            {artist.name}
          </h1>
          <div className="flex flex-wrap gap-2 justify-center md:justify-start">
            {artist.genres.map((genre) => (
              <span
                key={genre}
                className="px-2 py-1 bg-secondary text-secondary-foreground rounded-full text-sm"
              >
                {genre}
              </span>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div className="flex flex-col sm:flex-row gap-4 text-sm text-muted-foreground">
          <div>
            <span className="font-semibold text-foreground">
              {formatFollowers(artist.followers)}
            </span>{' '}
            followers
          </div>
          <div>
            <span className="font-semibold text-foreground">
              {formatMonthlyListeners(artist.monthlyListeners)}
            </span>{' '}
            monthly listeners
          </div>
        </div>

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
          <button
            onClick={handleFollowClick}
            className={`px-6 py-2 rounded-full font-medium transition-colors ${
              isFollowing
                ? 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                : 'bg-primary text-primary-foreground hover:bg-primary/80'
            }`}
          >
            {isFollowing ? 'Following' : 'Follow'}
          </button>
          
          <button className="px-6 py-2 rounded-full border border-border text-foreground hover:bg-accent hover:text-accent-foreground transition-colors">
            Share
          </button>
        </div>

        {/* Social Links */}
        {artist.socialLinks && (
          <div className="flex gap-3 justify-center md:justify-start">
            {artist.socialLinks.spotify && (
              <a
                href={artist.socialLinks.spotify}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Spotify"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
                </svg>
              </a>
            )}
            {artist.socialLinks.instagram && (
              <a
                href={artist.socialLinks.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Instagram"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C8.396 0 7.989.013 7.041.048 6.094.082 5.52.204 5.02.43c-.537.233-.99.542-1.44.991-.45.45-.758.903-.99 1.44C2.364 3.361 2.242 3.935 2.208 4.882 2.172 5.83 2.16 6.237 2.16 9.858s.013 4.028.048 4.976c.034.947.156 1.521.382 2.021.233.537.542.99.991 1.44.45.45.903.758 1.44.99.5.227 1.074.349 2.021.383.948.034 1.355.046 4.976.046s4.028-.012 4.976-.046c.947-.034 1.521-.156 2.021-.383a3.9 3.9 0 001.44-.99c.45-.45.758-.903.99-1.44.227-.5.349-1.074.383-2.021.034-.948.046-1.355.046-4.976s-.012-4.028-.046-4.976c-.034-.947-.156-1.521-.383-2.021a3.9 3.9 0 00-.99-1.44c-.45-.45-.903-.758-1.44-.99-.5-.227-1.074-.349-2.021-.383C16.045.013 15.638 0 12.017 0zM12.017 2.16c3.557 0 3.98.013 5.385.048.3.014.462.064.57.107.143.056.245.122.353.23.108.108.174.21.23.353.043.108.093.27.107.57.035 1.405.048 1.828.048 5.385s-.013 3.98-.048 5.385c-.014.3-.064.462-.107.57-.056.143-.122.245-.23.353a.926.926 0 01-.353.23c-.108.043-.27.093-.57.107-1.405.035-1.828.048-5.385.048s-3.98-.013-5.385-.048c-.3-.014-.462-.064-.57-.107a.926.926 0 01-.353-.23.926.926 0 01-.23-.353c-.043-.108-.093-.27-.107-.57-.035-1.405-.048-1.828-.048-5.385s.013-3.98.048-5.385c.014-.3.064-.462.107-.57.056-.143.122-.245.23-.353a.926.926 0 01.353-.23c.108-.043.27-.093.57-.107 1.405-.035 1.828-.048 5.385-.048zm0 3.678a5.322 5.322 0 100 10.644 5.322 5.322 0 000-10.644zm0 8.784a3.462 3.462 0 110-6.924 3.462 3.462 0 010 6.924zm6.584-9.845a1.243 1.243 0 11-2.486 0 1.243 1.243 0 012.486 0z"/>
                </svg>
              </a>
            )}
            {artist.socialLinks.twitter && (
              <a
                href={artist.socialLinks.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-foreground transition-colors"
                aria-label="Twitter"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
