'use client';

import { useTranslations } from 'next-intl';
import { useMockArtistData } from '@/features/artist/hooks/useArtistData';
import { Track } from '@/features/artist/types';
import ArtistHeaderShadcn from '@/features/artist/components/ArtistHeaderShadcn';
import TopTracksShadcn from '@/features/artist/components/TopTracksShadcn';

interface ArtistPageContentProps {
  artistId: string;
  locale: string;
}

export function ArtistPageContent({ artistId, locale }: ArtistPageContentProps) {
  const t = useTranslations('artist');
  const tCommon = useTranslations('common');
  const { artist, isLoading, error } = useMockArtistData(artistId);

  // Mock tracks data - in real app, this would come from another hook
  const mockTracks: Track[] = [
    {
      id: '1',
      title: 'Blinding Lights',
      duration: 200,
      playCount: 2800000000,
      albumName: 'After Hours',
      releaseDate: '2019-11-29',
      explicit: false,
    },
    {
      id: '2',
      title: 'The Hills',
      duration: 242,
      playCount: 1900000000,
      albumName: 'Beauty Behind the Madness',
      releaseDate: '2015-05-27',
      explicit: true,
    },
    {
      id: '3',
      title: 'Starboy',
      duration: 230,
      playCount: 1700000000,
      albumName: 'Starboy',
      releaseDate: '2016-09-22',
      explicit: true,
    },
    {
      id: '4',
      title: 'Can\'t Feel My Face',
      duration: 213,
      playCount: 1500000000,
      albumName: 'Beauty Behind the Madness',
      releaseDate: '2015-06-08',
      explicit: false,
    },
    {
      id: '5',
      title: 'Earned It',
      duration: 258,
      playCount: 1200000000,
      albumName: 'Beauty Behind the Madness',
      releaseDate: '2014-12-23',
      explicit: true,
    },
  ];

  const handleTrackPlay = (track: Track) => {
    console.log('Playing track:', track.title);
    // In a real app, this would trigger the audio player
  };

  const handleFollow = () => {
    console.log('Following artist:', artist?.name);
    // In a real app, this would call an API to follow the artist
  };

  const handleUnfollow = () => {
    console.log('Unfollowing artist:', artist?.name);
    // In a real app, this would call an API to unfollow the artist
  };

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">{tCommon('error')}</h1>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Artist Header Section */}
      <ArtistHeaderShadcn
        artist={artist!}
        isLoading={isLoading}
        isFollowing={false}
        onFollow={handleFollow}
        onUnfollow={handleUnfollow}
        locale={locale}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 space-y-12">
        {/* Top Tracks Section */}
        <TopTracksShadcn
          tracks={mockTracks}
          onTrackPlay={handleTrackPlay}
          isLoading={isLoading}
          locale={locale}
        />

        {/* Future sections would go here */}
        {/* <ArtistBio bio={artist?.bio} /> */}
        {/* <ArtistDiscography albums={albums} /> */}
        {/* <RelatedArtists artists={relatedArtists} /> */}
        {/* <ArtistStats stats={stats} /> */}
      </div>
    </div>
  );
}
