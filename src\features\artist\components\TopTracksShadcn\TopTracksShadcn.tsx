'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { TopTracksProps } from '../../types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  PlayIcon, 
  PauseIcon,
  EllipsisHorizontalIcon,
  MusicalNoteIcon,
  SpeakerWaveIcon
} from '@heroicons/react/24/solid';

interface TopTracksShadcnProps extends TopTracksProps {
  locale: string;
}

export default function TopTracksShadcn({
  tracks,
  onTrackPlay,
  currentPlayingId,
  isLoading = false,
  locale,
}: TopTracksShadcnProps) {
  const t = useTranslations('artist');
  const [hoveredTrack, setHoveredTrack] = useState<string | null>(null);

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatPlayCount = (count: number): string => {
    if (count >= 1000000000) {
      return `${(count / 1000000000).toFixed(1)}B`;
    }
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MusicalNoteIcon className="w-5 h-5" />
            <Skeleton className="h-6 w-48" />
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center gap-4 p-3 rounded-lg">
              <Skeleton className="w-8 h-8" />
              <Skeleton className="w-12 h-12 rounded" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-12" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (!tracks || tracks.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MusicalNoteIcon className="w-5 h-5" />
            {t('popularTracks')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <MusicalNoteIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
            {t('noTracksAvailable')}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MusicalNoteIcon className="w-5 h-5" />
          {t('popularTracks')}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-1">
        {tracks.map((track, index) => {
          const isPlaying = currentPlayingId === track.id;
          const isHovered = hoveredTrack === track.id;
          
          return (
            <div
              key={track.id}
              className={`flex items-center gap-4 p-3 rounded-lg transition-all duration-200 cursor-pointer group hover:bg-accent/50 ${
                isHovered ? 'bg-accent' : ''
              } ${isPlaying ? 'bg-primary/10' : ''}`}
              onMouseEnter={() => setHoveredTrack(track.id)}
              onMouseLeave={() => setHoveredTrack(null)}
              onClick={() => onTrackPlay?.(track)}
            >
              {/* Track Number / Play Button */}
              <div className="w-8 flex items-center justify-center">
                {isPlaying ? (
                  <div className="w-4 h-4 flex items-center justify-center">
                    <SpeakerWaveIcon className="w-4 h-4 text-primary animate-pulse" />
                  </div>
                ) : isHovered ? (
                  <Button size="sm" variant="ghost" className="w-8 h-8 p-0">
                    <PlayIcon className="w-4 h-4" />
                  </Button>
                ) : (
                  <span className="text-sm text-muted-foreground font-medium">
                    {index + 1}
                  </span>
                )}
              </div>

              {/* Album Cover Placeholder */}
              <Avatar className="w-12 h-12 rounded">
                <AvatarFallback className="rounded">
                  <MusicalNoteIcon className="w-6 h-6 text-muted-foreground" />
                </AvatarFallback>
              </Avatar>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h3
                    className={`font-medium truncate ${
                      isPlaying ? 'text-primary' : 'text-foreground'
                    }`}
                  >
                    {track.title}
                  </h3>
                  {track.explicit && (
                    <Badge variant="secondary" className="text-xs px-1 py-0">
                      E
                    </Badge>
                  )}
                </div>
                {track.albumName && (
                  <p className="text-sm text-muted-foreground truncate">
                    {track.albumName}
                  </p>
                )}
              </div>

              {/* Play Count */}
              <div className="hidden sm:block text-sm text-muted-foreground">
                {formatPlayCount(track.playCount)}
              </div>

              {/* Duration */}
              <div className="text-sm text-muted-foreground font-mono min-w-[40px] text-right">
                {formatDuration(track.duration)}
              </div>

              {/* More Options */}
              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100 w-8 h-8 p-0 transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  // Handle more options
                }}
              >
                <EllipsisHorizontalIcon className="w-4 h-4" />
              </Button>
            </div>
          );
        })}

        {tracks.length >= 5 && (
          <div className="pt-4">
            <Button variant="ghost" className="w-full">
              {t('showAllTracks')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
