'use client';

import { ArtistHeader, TopTracks } from '@/features/artist';
import { useMockArtistData } from '@/features/artist/hooks/useArtistData';
import { Track } from '@/features/artist/types';

interface ArtistPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ArtistPage({ params }: ArtistPageProps) {
  const { id } = await params;

  return <ArtistPageContent artistId={id} />;
}

function ArtistPageContent({ artistId }: { artistId: string }) {
  const { artist, isLoading, error } = useMockArtistData(artistId);

  // Mock tracks data - in real app, this would come from another hook
  const mockTracks: Track[] = [
    {
      id: '1',
      title: 'Blinding Lights',
      duration: 200,
      playCount: 2800000000,
      albumName: 'After Hours',
      albumCover: 'https://example.com/after-hours.jpg',
      releaseDate: '2019-11-29',
      explicit: false,
    },
    {
      id: '2',
      title: 'The Hills',
      duration: 242,
      playCount: 1900000000,
      albumName: 'Beauty Behind the Madness',
      albumCover: 'https://example.com/beauty-behind.jpg',
      releaseDate: '2015-05-27',
      explicit: true,
    },
    {
      id: '3',
      title: 'Starboy',
      duration: 230,
      playCount: 1700000000,
      albumName: 'Starboy',
      albumCover: 'https://example.com/starboy.jpg',
      releaseDate: '2016-09-22',
      explicit: true,
    },
    {
      id: '4',
      title: 'Can\'t Feel My Face',
      duration: 213,
      playCount: 1500000000,
      albumName: 'Beauty Behind the Madness',
      albumCover: 'https://example.com/beauty-behind.jpg',
      releaseDate: '2015-06-08',
      explicit: false,
    },
    {
      id: '5',
      title: 'Earned It',
      duration: 258,
      playCount: 1200000000,
      albumName: 'Beauty Behind the Madness',
      albumCover: 'https://example.com/beauty-behind.jpg',
      releaseDate: '2014-12-23',
      explicit: true,
    },
  ];

  const handleTrackPlay = (track: Track) => {
    console.log('Playing track:', track.title);
    // In a real app, this would trigger the audio player
  };

  const handleFollow = () => {
    console.log('Following artist:', artist?.name);
    // In a real app, this would call an API to follow the artist
  };

  const handleUnfollow = () => {
    console.log('Unfollowing artist:', artist?.name);
    // In a real app, this would call an API to unfollow the artist
  };

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">Error</h1>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Artist Header Section */}
      <ArtistHeader
        artist={artist!}
        isLoading={isLoading}
        isFollowing={false}
        onFollow={handleFollow}
        onUnfollow={handleUnfollow}
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8 space-y-12">
        {/* Top Tracks Section */}
        <TopTracks
          tracks={mockTracks}
          onTrackPlay={handleTrackPlay}
          isLoading={isLoading}
        />

        {/* Future sections would go here */}
        {/* <ArtistBio bio={artist?.bio} /> */}
        {/* <ArtistDiscography albums={albums} /> */}
        {/* <RelatedArtists artists={relatedArtists} /> */}
        {/* <ArtistStats stats={stats} /> */}
      </div>
    </div>
  );
}
