export const translations = {
  en: {
    HomePage: {
      title: 'Smash Music',
      subtitle: 'A modern music application',
      viewArtist: 'View Artist Page Demo'
    },
    ArtistPage: {
      followers: 'followers',
      monthlyListeners: 'monthly listeners',
      follow: 'Follow',
      following: 'Following',
      share: 'Share',
      play: 'Play',
      popularTracks: 'Popular Tracks',
      showAllTracks: 'Show all tracks',
      noTracksAvailable: 'No tracks available',
      explicit: 'Explicit'
    },
    Common: {
      loading: 'Loading...',
      error: 'Error',
      theme: 'Theme',
      language: 'Language'
    }
  },
  es: {
    HomePage: {
      title: 'Smash Music',
      subtitle: 'Una aplicación de música moderna',
      viewArtist: 'Ver Demo de Página de Artista'
    },
    ArtistPage: {
      followers: 'seguidores',
      monthlyListeners: 'oyentes mensuales',
      follow: 'Seguir',
      following: 'Siguiendo',
      share: 'Compartir',
      play: 'Reproducir',
      popularTracks: 'Canciones Populares',
      showAllTracks: 'Mostrar todas las canciones',
      noTracksAvailable: 'No hay canciones disponibles',
      explicit: 'Explícito'
    },
    Common: {
      loading: 'Cargando...',
      error: 'Error',
      theme: 'Tema',
      language: 'Idioma'
    }
  },
  fr: {
    HomePage: {
      title: 'Smash Music',
      subtitle: 'Une application musicale moderne',
      viewArtist: 'Voir la Démo de Page d\'Artiste'
    },
    ArtistPage: {
      followers: 'abonnés',
      monthlyListeners: 'auditeurs mensuels',
      follow: 'Suivre',
      following: 'Suivi',
      share: 'Partager',
      play: 'Jouer',
      popularTracks: 'Titres Populaires',
      showAllTracks: 'Afficher tous les titres',
      noTracksAvailable: 'Aucun titre disponible',
      explicit: 'Explicite'
    },
    Common: {
      loading: 'Chargement...',
      error: 'Erreur',
      theme: 'Thème',
      language: 'Langue'
    }
  }
} as const;

export type Language = keyof typeof translations;
export type TranslationKey = keyof typeof translations.en;
